| 一级功能（功能大类） | 二级功能          | 功能描述                                            | 功能分类 | 需求专业 | 需求人 | 产品经理 | 需求分类 | 具体成效（需求人填写）                                                                                                              | 人天  | 责任人 |     |
| ---------- | ------------- | ----------------------------------------------- | ---- | ---- | --- | ---- | ---- | ------------------------------------------------------------------------------------------------------------------------ | --- | --- | --- |
| 资源开通优化     | 融合边缘云开通优化     | （1）GPU云主机支持自动开通一机多卡  <br>（2）地址段单独开通和绑定纳入算力工作台功能 | 运营功能 | 综合   | 付鑫如 | 王瑞玲  | 专业需求 | 类型：降本  <br>成效：节省工时6人天/年  <br>计算依据：人工配置，预计每单业务需要90分钟，经算力工作台优化后，自动下发执行，减少人工60分钟，统计融合边缘云一年有50次相关需求，全年合计节约60*50/60/8=6人天     | 350 | 李佳  |     |
| 提升PaaS服务能力 | 中间件服务日志监控管理功能 | 基于云枢平台，FI、宝兰德等中间件实现日志的一站式采集、处理、存储、归档及查询。        | 运维功能 | PaaS | 王敏  | 姚宗山  | 专业需求 | 类型：降本、提效  <br>节省工时124人天/年  <br>计算依据：自动化前，共8人，每人每天约消耗60分钟，自动化后约30分钟，功能使用主要集中在工作日，共 ​​248天，故全年合计节约8*（60-30）*248/60/8=124人天 | 240 | 李佳  |     |


